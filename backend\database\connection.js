// PostgreSQL database connection using pg
const { Pool } = require('pg')
require('dotenv').config()

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  user: process.env.PGUSER || 'postgres',
  password: process.env.PGPASSWORD || '120852',
  database: process.env.PGDATABASE || 'Chergui',
  port: process.env.PGPORT ? parseInt(process.env.PGPORT) : 5432,
})

pool.on('connect', () => {
  console.log('Connected to PostgreSQL database')
})

pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err)
  process.exit(-1)
})

module.exports = pool
