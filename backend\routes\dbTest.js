const express = require('express')
const router = express.Router()
const pool = require('../database/connection')

// Test DB connection
router.get('/test-db', async (req, res) => {
  try {
    const result = await pool.query('SELECT NOW()')
    res.json({ success: true, time: result.rows[0].now })
  } catch (err) {
    res.status(500).json({ success: false, error: err.message })
  }
})

module.exports = router
